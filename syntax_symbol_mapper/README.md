# SyntaxToSymbolMapper

A C++ module for mapping SystemVerilog SyntaxTree nodes to Symbol objects using the slang library.

## Overview

This module provides efficient bidirectional mapping between syntax nodes and semantic symbols in SystemVerilog code. It's built on top of the slang SystemVerilog parser and semantic analyzer, offering:

- **Bidirectional Mapping**: Map from syntax nodes to symbols and vice versa
- **Efficient Caching**: LRU cache for improved performance on repeated lookups
- **Symbol Resolution**: Advanced symbol resolution with scope traversal
- **Comprehensive API**: Easy-to-use interface for symbol analysis

## Features

### Core Components

1. **SyntaxToSymbolMapper**: Main class providing mapping functionality
2. **SymbolResolver**: Advanced symbol resolution and scope analysis
3. **MappingCache**: High-performance LRU cache for symbol mappings
4. **Common Utilities**: Helper functions for symbol and syntax analysis

### Key Capabilities

- Map syntax nodes to their corresponding symbols
- Reverse lookup from symbols to syntax nodes
- Scope-aware symbol resolution
- Identifier reference resolution
- Symbol declaration detection
- Qualified name generation
- Performance optimization through caching

## Building

### Prerequisites

- C++20 compatible compiler
- CMake 3.20 or later
- slang library (libsvlang.a)

### Build Instructions

```bash
cd syntax_symbol_mapper
mkdir build && cd build
cmake ..
make
```

### Running Tests

```bash
# Basic tests
./test_mapper

# Comprehensive tests
./test_comprehensive

# Performance tests
./test_performance

# All tests via CTest
ctest
```

## Usage

### Basic Example

```cpp
#include "SyntaxToSymbolMapper.h"
#include "slang/syntax/SyntaxTree.h"
#include "slang/ast/Compilation.h"

using namespace syntax_symbol_mapper;
using namespace slang;

// Parse SystemVerilog code
std::string source = R"(
module example;
    int my_variable;
    logic [7:0] data_bus;
endmodule
)";

auto tree = syntax::SyntaxTree::fromText(source);
ast::Compilation compilation;
compilation.addSyntaxTree(tree);

// Create mapper
SyntaxToSymbolMapper mapper(compilation);

// Build mapping
auto result = mapper.buildMapping(*tree);
if (result.success) {
    std::cout << "Mapped " << result.mapped_count << " symbols" << std::endl;
}

// Look up symbols
const auto& syntaxToSymbol = mapper.getSyntaxToSymbolMap();
for (const auto& [syntax, symbol] : syntaxToSymbol) {
    auto info = mapper.getSymbolInfo(syntax);
    std::cout << "Symbol: " << info.name << " at " << info.location << std::endl;
}
```

### Advanced Configuration

```cpp
// Configure mapper behavior
MapperConfig config;
config.enable_caching = true;
config.deep_traversal = true;
config.include_implicit_symbols = false;
config.max_cache_size = 10000;

SyntaxToSymbolMapper mapper(compilation, config);
```

### Symbol Resolution

```cpp
#include "SymbolResolver.h"

SymbolResolver resolver(compilation);

// Check if a node is a symbol declaration
if (resolver.isSymbolDeclaration(syntaxNode)) {
    auto symbol = resolver.findSymbolFromSyntax(syntaxNode);
    // Process symbol...
}

// Resolve identifier references
if (resolver.isIdentifierReference(syntaxNode)) {
    auto symbol = resolver.resolveIdentifier(syntaxNode);
    // Process resolved symbol...
}

// Get qualified names
std::string qualifiedName = resolver.getQualifiedName(syntaxNode);
```

### Cache Management

```cpp
#include "MappingCache.h"

MappingCache cache(5000); // Max 5000 entries

// Add mappings
cache.addMapping(syntaxNode, symbol);

// Lookup
auto cachedSymbol = cache.findSymbol(syntaxNode);
auto cachedSyntax = cache.findSyntax(symbol);

// Get statistics
auto stats = cache.getStats();
std::cout << "Hit rate: " << (stats.hit_rate * 100) << "%" << std::endl;
```

## API Reference

### SyntaxToSymbolMapper

#### Constructor
```cpp
SyntaxToSymbolMapper(Compilation& compilation, const MapperConfig& config = {});
```

#### Core Methods
```cpp
MappingResult buildMapping(const SyntaxTree& tree);
const Symbol* findSymbol(const SyntaxNode* syntax) const;
const SyntaxNode* findSyntax(const Symbol* symbol) const;
SymbolInfo getSymbolInfo(const SyntaxNode* syntax) const;
```

#### Utility Methods
```cpp
size_t getMappingCount() const;
bool isMapped(const SyntaxNode* syntax) const;
void clear();
void updateConfig(const MapperConfig& config);
```

### SymbolResolver

#### Symbol Analysis
```cpp
const Symbol* resolveSymbol(const SyntaxNode* syntax);
const Symbol* resolveIdentifier(const SyntaxNode* identifierNode);
const Symbol* findSymbolFromSyntax(const SyntaxNode* syntax);
```

#### Scope Analysis
```cpp
const Scope* getScopeForSyntax(const SyntaxNode* syntax);
const Scope* findContainingScope(const SyntaxNode* syntax);
```

#### Syntax Classification
```cpp
bool isSymbolDeclaration(const SyntaxNode* syntax);
bool isIdentifierReference(const SyntaxNode* syntax);
std::string getIdentifierName(const SyntaxNode* syntax);
std::string getQualifiedName(const SyntaxNode* syntax);
```

### Configuration Options

```cpp
struct MapperConfig {
    bool enable_caching = true;        // Enable LRU cache
    bool deep_traversal = true;        // Traverse all syntax nodes
    bool include_implicit_symbols = false; // Include compiler-generated symbols
    size_t max_cache_size = 10000;     // Maximum cache entries
};
```

## Performance

The mapper is optimized for performance with:

- **O(1) symbol lookups** with caching enabled
- **LRU cache eviction** to manage memory usage
- **Lazy evaluation** for expensive operations
- **Efficient traversal** algorithms

Typical performance on modern hardware:
- Small modules (< 1000 lines): < 10ms
- Medium modules (< 10000 lines): < 100ms
- Large modules (< 100000 lines): < 1s

## Error Handling

The mapper handles various error conditions gracefully:

- **Invalid syntax**: Continues processing valid portions
- **Null pointers**: Returns nullptr safely
- **Missing symbols**: Returns empty results without crashing
- **Cache overflow**: Automatically evicts old entries

## Integration

This module integrates seamlessly with:

- **slang parser**: Uses slang's SyntaxTree and Compilation
- **LSP servers**: Provides symbol information for language servers
- **Code analysis tools**: Enables semantic analysis of SystemVerilog
- **IDE plugins**: Supports jump-to-definition and symbol search

## License

This project follows the same license as the slang library (MIT License).

## Contributing

Contributions are welcome! Please ensure:

1. Code follows C++20 standards
2. All tests pass
3. New features include tests
4. Documentation is updated

## Support

For issues and questions:

1. Check the test files for usage examples
2. Review the API documentation
3. Examine the slang library documentation
4. Create an issue with detailed reproduction steps
