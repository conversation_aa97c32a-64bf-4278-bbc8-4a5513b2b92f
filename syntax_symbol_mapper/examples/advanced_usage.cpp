#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <unordered_set>

#include "SyntaxToSymbolMapper.h"
#include "SymbolResolver.h"
#include "slang/syntax/SyntaxTree.h"
#include "slang/ast/Compilation.h"

using namespace syntax_symbol_mapper;
using namespace slang;

class SystemVerilogAnalyzer {
public:
    explicit SystemVerilogAnalyzer() : compilation_(), mapper_(compilation_), resolver_(compilation_) {
        // Configure mapper for optimal performance
        MapperConfig config;
        config.enable_caching = true;
        config.deep_traversal = true;
        config.max_cache_size = 50000;
        mapper_.updateConfig(config);
    }

    void reset() {
        // Just clear the trees - we'll create a new analyzer instance if needed
        trees_.clear();
    }

    bool loadFile(const std::string& filename) {
        std::ifstream file(filename);
        if (!file.is_open()) {
            std::cerr << "Error: Cannot open file " << filename << std::endl;
            return false;
        }

        std::string content((std::istreambuf_iterator<char>(file)),
                           std::istreambuf_iterator<char>());

        auto tree = syntax::SyntaxTree::fromText(content);
        if (!tree) {
            std::cerr << "Error: Failed to parse " << filename << std::endl;
            return false;
        }

        compilation_.addSyntaxTree(tree);
        trees_.push_back(tree);

        std::cout << "Loaded: " << filename << std::endl;
        return true;
    }

    bool analyze() {
        try {
            // Build mappings for all trees first
            for (const auto& tree : trees_) {
                auto result = mapper_.buildMapping(*tree);
                if (!result.success) {
                    std::cerr << "Error: Failed to build mapping - " << result.error_message << std::endl;
                    return false;
                }
                std::cout << "Mapped " << result.mapped_count << " symbols in tree" << std::endl;
            }

            std::cout << "Total mappings: " << mapper_.getMappingCount() << std::endl;
            return true;
        }
        catch (const std::exception& e) {
            std::cerr << "Analysis error: " << e.what() << std::endl;
            return false;
        }
    }

    void printSymbolStatistics() {
        std::cout << "\n=== Symbol Statistics ===" << std::endl;

        const auto& syntaxToSymbol = mapper_.getSyntaxToSymbolMap();

        // Count different types of symbols
        std::unordered_map<std::string, int> symbolTypeCounts;
        std::unordered_map<std::string, int> syntaxTypeCounts;

        for (const auto& [syntax, symbol] : syntaxToSymbol) {
            auto info = mapper_.getSymbolInfo(syntax);
            symbolTypeCounts[info.kind]++;
            syntaxTypeCounts[getSyntaxKindString(syntax)]++;
        }

        std::cout << "Symbol types found:" << std::endl;
        for (const auto& [type, count] : symbolTypeCounts) {
            std::cout << "  " << type << ": " << count << std::endl;
        }

        std::cout << "\nSyntax types mapped:" << std::endl;
        for (const auto& [type, count] : syntaxTypeCounts) {
            std::cout << "  " << type << ": " << count << std::endl;
        }
    }

    void findSymbolDeclarations() {
        std::cout << "\n=== Symbol Declarations ===" << std::endl;

        const auto& syntaxToSymbol = mapper_.getSyntaxToSymbolMap();

        for (const auto& [syntax, symbol] : syntaxToSymbol) {
            if (resolver_.isSymbolDeclaration(syntax)) {
                auto info = mapper_.getSymbolInfo(syntax);
                std::cout << "Declaration: " << info.name
                         << " (" << info.kind << ") at " << info.location << std::endl;

                // Get qualified name
                auto qualifiedName = resolver_.getQualifiedName(syntax);
                if (!qualifiedName.empty()) {
                    std::cout << "  Qualified: " << qualifiedName << std::endl;
                }
            }
        }
    }

    void findIdentifierReferences() {
        std::cout << "\n=== Identifier References ===" << std::endl;

        const auto& syntaxToSymbol = mapper_.getSyntaxToSymbolMap();
        int refCount = 0;

        for (const auto& [syntax, symbol] : syntaxToSymbol) {
            if (resolver_.isIdentifierReference(syntax)) {
                auto identifierName = resolver_.getIdentifierName(syntax);
                auto resolvedSymbol = resolver_.resolveIdentifier(syntax);

                std::cout << "Reference: " << identifierName;
                if (resolvedSymbol) {
                    std::cout << " -> resolved to symbol";
                } else {
                    std::cout << " -> unresolved";
                }
                std::cout << " at " << getLocationString(syntax) << std::endl;

                refCount++;
                if (refCount >= 10) { // Limit output
                    std::cout << "  ... and more" << std::endl;
                    break;
                }
            }
        }
    }

    void analyzeScopes() {
        std::cout << "\n=== Scope Analysis ===" << std::endl;

        const auto& syntaxToSymbol = mapper_.getSyntaxToSymbolMap();
        std::unordered_set<const Scope*> scopes;

        for (const auto& [syntax, symbol] : syntaxToSymbol) {
            auto scope = resolver_.getScopeForSyntax(syntax);
            if (scope) {
                scopes.insert(scope);
            }

            auto containingScope = resolver_.findContainingScope(syntax);
            if (containingScope) {
                scopes.insert(containingScope);
            }
        }

        std::cout << "Found " << scopes.size() << " unique scopes" << std::endl;

        // Analyze scope hierarchy
        for (const auto& scope : scopes) {
            if (scope) {
                // Count members manually since size() may not be available
                int memberCount = 0;
                for (auto& member : scope->members()) {
                    (void)member; // Suppress unused variable warning
                    memberCount++;
                }
                std::cout << "Scope with " << memberCount << " members" << std::endl;
            }
        }
    }

    void performanceAnalysis() {
        std::cout << "\n=== Performance Analysis ===" << std::endl;

        auto start = std::chrono::high_resolution_clock::now();

        // Perform many lookups to test cache performance
        const auto& syntaxToSymbol = mapper_.getSyntaxToSymbolMap();
        int lookupCount = 0;

        for (int i = 0; i < 1000; i++) {
            for (const auto& [syntax, symbol] : syntaxToSymbol) {
                auto foundSymbol = mapper_.findSymbol(syntax);
                auto foundSyntax = mapper_.findSyntax(symbol);
                (void)foundSymbol; // Suppress unused variable warning
                (void)foundSyntax; // Suppress unused variable warning
                lookupCount += 2;

                if (lookupCount >= 10000) break; // Limit test
            }
            if (lookupCount >= 10000) break;
        }

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);

        std::cout << "Performed " << lookupCount << " lookups in "
                  << duration.count() << " microseconds" << std::endl;
        std::cout << "Average lookup time: "
                  << (static_cast<double>(duration.count()) / lookupCount)
                  << " μs per lookup" << std::endl;
    }

private:
    ast::Compilation compilation_;
    SyntaxToSymbolMapper mapper_;
    SymbolResolver resolver_;
    std::vector<std::shared_ptr<syntax::SyntaxTree>> trees_;
};

void demonstrateAdvancedAnalysis() {
    std::cout << "=== Advanced Analysis Demo ===" << std::endl;

    // Create a sample SystemVerilog code directly
    std::string sampleCode = R"(
package demo_pkg;
    typedef struct {
        int x, y, z;
    } point3d_t;

    parameter int MAX_COUNT = 1000;
endpackage

module demo_module #(
    parameter int WIDTH = 8,
    parameter int DEPTH = 16
) (
    input logic clk,
    input logic reset,
    input logic [WIDTH-1:0] data_in,
    output logic [WIDTH-1:0] data_out,
    output logic valid
);
    import demo_pkg::*;

    // Internal signals
    logic [WIDTH-1:0] buffer [0:DEPTH-1];
    int write_ptr, read_ptr;
    point3d_t coordinates;

    // Functions
    function automatic logic [WIDTH-1:0] process_data(logic [WIDTH-1:0] input_data);
        return input_data ^ 8'hAA;
    endfunction

    function automatic int increment_pointer(int ptr);
        return (ptr + 1) % DEPTH;
    endfunction

    // Main logic
    always_ff @(posedge clk or posedge reset) begin
        if (reset) begin
            write_ptr <= 0;
            read_ptr <= 0;
            valid <= 0;
            coordinates <= '{default: 0};
        end else begin
            buffer[write_ptr] <= process_data(data_in);
            write_ptr <= increment_pointer(write_ptr);

            data_out <= buffer[read_ptr];
            read_ptr <= increment_pointer(read_ptr);

            valid <= (write_ptr != read_ptr);

            coordinates.x <= coordinates.x + 1;
            coordinates.y <= coordinates.y + 2;
            coordinates.z <= coordinates.z + 3;
        end
    end

endmodule
)";

    // Parse and analyze directly
    auto tree = syntax::SyntaxTree::fromText(sampleCode);
    if (!tree) {
        std::cerr << "Failed to parse sample code" << std::endl;
        return;
    }

    ast::Compilation compilation;
    compilation.addSyntaxTree(tree);

    SyntaxToSymbolMapper mapper(compilation);
    SymbolResolver resolver(compilation);

    // Build mapping
    auto result = mapper.buildMapping(*tree);
    if (result.success) {
        std::cout << "✓ Successfully mapped " << result.mapped_count << " symbols" << std::endl;
        std::cout << "✓ Total mappings: " << mapper.getMappingCount() << std::endl;

        // Demonstrate advanced features
        const auto& syntaxToSymbol = mapper.getSyntaxToSymbolMap();

        std::cout << "\n=== Symbol Analysis ===" << std::endl;
        int count = 0;
        for (const auto& [syntax, symbol] : syntaxToSymbol) {
            auto info = mapper.getSymbolInfo(syntax);
            std::cout << "Symbol: " << info.name << " (" << info.kind << ") at " << info.location << std::endl;

            if (resolver.isSymbolDeclaration(syntax)) {
                std::cout << "  -> This is a symbol declaration" << std::endl;
            }

            if (resolver.isIdentifierReference(syntax)) {
                std::cout << "  -> This is an identifier reference" << std::endl;
            }

            count++;
            if (count >= 5) break; // Limit output
        }

        std::cout << "\n✓ Advanced analysis completed successfully!" << std::endl;
    } else {
        std::cerr << "Failed to build mapping: " << result.error_message << std::endl;
    }
}

int main() {
    std::cout << "Advanced SyntaxToSymbolMapper Usage Examples" << std::endl;
    std::cout << "============================================" << std::endl;

    try {
        demonstrateAdvancedAnalysis();

        std::cout << "\n✓ Advanced usage demonstration completed!" << std::endl;
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "\n✗ Advanced usage failed: " << e.what() << std::endl;
        return 1;
    }
}
