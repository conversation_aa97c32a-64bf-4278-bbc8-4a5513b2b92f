#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <functional>

#include "SyntaxToSymbolMapper.h"
#include "SymbolResolver.h"
#include "slang/syntax/SyntaxTree.h"
#include "slang/ast/Compilation.h"
#include "slang/ast/Symbol.h"
#include "slang/ast/Scope.h"
#include "slang/ast/symbols/CompilationUnitSymbols.h"

using namespace syntax_symbol_mapper;
using namespace slang;

void printSyntaxTree(const syntax::SyntaxNode& node, int depth = 0) {
    std::string indent(depth * 2, ' ');
    std::cout << indent << "SyntaxKind: " << toString(node.kind) << std::endl;

    // Use getChildCount() and childNode() instead of childNodes()
    size_t childCount = node.getChildCount();
    for (size_t i = 0; i < childCount; i++) {
        auto child = node.childNode(i);
        if (child) {
            printSyntaxTree(*child, depth + 1);
        }
    }
}

void printSymbolHierarchy(const ast::Symbol& symbol, int depth = 0) {
    std::string indent(depth * 2, ' ');
    std::cout << indent << "Symbol: " << symbol.name << " (Kind: " << toString(symbol.kind) << ")" << std::endl;

    if (auto scope = symbol.as_if<ast::Scope>()) {
        for (auto& member : scope->members()) {
            printSymbolHierarchy(member, depth + 1);
        }
    }
}

int main() {
    std::cout << "=== Debug Analysis ===" << std::endl;

    // Create the same sample code
    std::string sampleCode = R"(
package demo_pkg;
    typedef struct {
        int x, y, z;
    } point3d_t;

    parameter int MAX_COUNT = 1000;
endpackage

module demo_module #(
    parameter int WIDTH = 8,
    parameter int DEPTH = 16
) (
    input logic clk,
    input logic reset,
    input logic [WIDTH-1:0] data_in,
    output logic [WIDTH-1:0] data_out,
    output logic valid
);
    import demo_pkg::*;

    // Internal signals
    logic [WIDTH-1:0] buffer [0:DEPTH-1];
    int write_ptr, read_ptr;
    point3d_t coordinates;

    // Functions
    function automatic logic [WIDTH-1:0] process_data(logic [WIDTH-1:0] input_data);
        return input_data ^ 8'hAA;
    endfunction

    function automatic int increment_pointer(int ptr);
        return (ptr + 1) % DEPTH;
    endfunction

    // Main logic
    always_ff @(posedge clk or posedge reset) begin
        if (reset) begin
            write_ptr <= 0;
            read_ptr <= 0;
            valid <= 0;
            coordinates <= '{default: 0};
        end
        else begin
            buffer[write_ptr] <= process_data(data_in);
            write_ptr <= increment_pointer(write_ptr);

            data_out <= buffer[read_ptr];
            read_ptr <= increment_pointer(read_ptr);

            valid <= (write_ptr != read_ptr);

            coordinates.x <= coordinates.x + 1;
            coordinates.y <= coordinates.y + 2;
            coordinates.z <= coordinates.z + 3;
        end
    end

endmodule
)";

    // Parse the code
    auto tree = syntax::SyntaxTree::fromText(sampleCode);
    if (!tree) {
        std::cerr << "Failed to parse sample code" << std::endl;
        return 1;
    }

    std::cout << "\n=== Syntax Tree Structure ===" << std::endl;
    printSyntaxTree(tree->root());

    // Create compilation and add the tree
    ast::Compilation compilation;
    compilation.addSyntaxTree(tree);

    std::cout << "\n=== Symbol Hierarchy (Before getRoot) ===" << std::endl;
    auto& rootBefore = compilation.getRootNoFinalize();
    std::cout << "Root Symbol: " << rootBefore.name << " (Kind: " << toString(rootBefore.kind) << ")" << std::endl;

    // Force compilation to complete
    std::cout << "\n=== Forcing Compilation to Complete ===" << std::endl;
    auto& root = compilation.getRoot();
    std::cout << "Root Symbol: " << root.name << " (Kind: " << toString(root.kind) << ")" << std::endl;

    // Print compilation units and their members recursively
    std::function<void(const ast::Symbol&, int)> printSymbolRecursive = [&](const ast::Symbol& symbol, int depth) {
        std::string indent(depth * 2, ' ');
        std::cout << indent << "Symbol: " << symbol.name << " (Kind: " << toString(symbol.kind) << ")" << std::endl;

        if (auto scope = symbol.as_if<ast::Scope>()) {
            for (auto& member : scope->members()) {
                printSymbolRecursive(member, depth + 1);
            }
        }
    };

    for (auto& unit : root.compilationUnits) {
        std::cout << "  CompilationUnit: " << unit->name << std::endl;
        printSymbolRecursive(*unit, 1);
    }

    // Try our mapper with different configurations
    std::cout << "\n=== Mapper Analysis ===" << std::endl;

    // Test with default config
    SyntaxToSymbolMapper mapper1(compilation);
    auto result1 = mapper1.buildMapping(*tree);
    std::cout << "Default config - Mapped count: " << result1.mapped_count << std::endl;

    // Test with enhanced config
    MapperConfig enhancedConfig;
    enhancedConfig.deep_traversal = true;
    enhancedConfig.include_implicit_symbols = true;
    enhancedConfig.enable_caching = true;

    SyntaxToSymbolMapper mapper2(compilation, enhancedConfig);
    auto result2 = mapper2.buildMapping(*tree);
    std::cout << "Enhanced config - Mapped count: " << result2.mapped_count << std::endl;

    // Use the better result
    auto& mapper = (result2.mapped_count > result1.mapped_count) ? mapper2 : mapper1;
    auto& result = (result2.mapped_count > result1.mapped_count) ? result2 : result1;

    std::cout << "Using mapper with " << result.mapped_count << " mappings" << std::endl;

    if (!result.success) {
        std::cout << "Error: " << result.error_message << std::endl;
    }

    // Export detailed results
    std::ofstream debugFile("debug_analysis_results.txt");
    if (debugFile.is_open()) {
        debugFile << "=== Debug Analysis Results ===" << std::endl;
        debugFile << "Mapping success: " << (result.success ? "YES" : "NO") << std::endl;
        debugFile << "Mapped count: " << result.mapped_count << std::endl;
        debugFile << "Total mappings: " << mapper.getMappingCount() << std::endl;

        if (!result.success) {
            debugFile << "Error message: " << result.error_message << std::endl;
        }

        debugFile << "\n=== Expected vs Actual Symbols ===" << std::endl;
        debugFile << "Expected symbols in the SystemVerilog code:" << std::endl;
        debugFile << "1. demo_pkg (package)" << std::endl;
        debugFile << "2. point3d_t (typedef)" << std::endl;
        debugFile << "3. x, y, z (struct members)" << std::endl;
        debugFile << "4. MAX_COUNT (parameter)" << std::endl;
        debugFile << "5. demo_module (module)" << std::endl;
        debugFile << "6. WIDTH, DEPTH (module parameters)" << std::endl;
        debugFile << "7. clk, reset, data_in, data_out, valid (ports)" << std::endl;
        debugFile << "8. buffer, write_ptr, read_ptr, coordinates (variables)" << std::endl;
        debugFile << "9. process_data, increment_pointer (functions)" << std::endl;
        debugFile << "10. input_data, ptr (function parameters)" << std::endl;
        debugFile << "Expected total: ~20 symbols" << std::endl;
        debugFile << "Actual found: " << result.mapped_count << " symbols" << std::endl;

        debugFile << "\n=== All Mappings Found ===" << std::endl;
        const auto& syntaxToSymbol = mapper.getSyntaxToSymbolMap();
        int index = 1;
        for (const auto& [syntax, symbol] : syntaxToSymbol) {
            auto info = mapper.getSymbolInfo(syntax);
            debugFile << "Mapping #" << index << ":" << std::endl;
            debugFile << "  Symbol Name: " << info.name << std::endl;
            debugFile << "  Symbol Kind: " << info.kind << std::endl;
            debugFile << "  Location: " << info.location << std::endl;
            debugFile << "  Syntax Kind: " << getSyntaxKindString(syntax) << std::endl;
            debugFile << std::endl;
            index++;
        }

        debugFile << "\n=== Symbol Hierarchy Found ===" << std::endl;
        std::function<void(const ast::Symbol&, int)> printSymbolToFile = [&](const ast::Symbol& symbol, int depth) {
            std::string indent(depth * 2, ' ');
            debugFile << indent << "Symbol: " << symbol.name << " (Kind: " << toString(symbol.kind) << ")" << std::endl;

            if (auto scope = symbol.as_if<ast::Scope>()) {
                for (auto& member : scope->members()) {
                    printSymbolToFile(member, depth + 1);
                }
            }
        };

        for (auto& unit : root.compilationUnits) {
            debugFile << "CompilationUnit: " << unit->name << std::endl;
            printSymbolToFile(*unit, 1);
        }

        debugFile.close();
        std::cout << "Debug results exported to: debug_analysis_results.txt" << std::endl;
    }

    return 0;
}
