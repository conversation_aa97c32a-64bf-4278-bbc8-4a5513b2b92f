#include "SymbolResolver.h"

#include "slang/ast/Compilation.h"
#include "slang/ast/Symbol.h"
#include "slang/ast/Scope.h"
#include "slang/ast/symbols/CompilationUnitSymbols.h"
#include "slang/syntax/SyntaxNode.h"
#include "slang/syntax/AllSyntax.h"

namespace syntax_symbol_mapper {

class SymbolResolver::Impl {
public:
    explicit Impl(Compilation& comp) : compilation(comp), rootScope(comp.getRoot().as<Scope>()) {}

    const Symbol* resolveSymbol(const SyntaxNode* syntax) {
        if (!syntax) return nullptr;

        // First check if this syntax node created a symbol directly
        auto directSymbol = findSymbolFromSyntax(syntax);
        if (directSymbol) return directSymbol;

        // If it's an identifier reference, resolve it
        if (isIdentifierReference(syntax)) {
            return resolveIdentifier(syntax);
        }

        return nullptr;
    }

    const Symbol* resolveIdentifier(const SyntaxNode* identifierNode) {
        if (!identifierNode || !isIdentifierReference(identifierNode)) {
            return nullptr;
        }

        std::string identifierName = getIdentifierName(identifierNode);
        if (identifierName.empty()) return nullptr;

        // Traverse up the syntax tree to find the containing scope
        const SyntaxNode* current = identifierNode->parent;
        while (current) {
            auto scope = getScopeForSyntax(current);
            if (scope) {
                // Look up the identifier in this scope
                auto symbol = scope->lookupName(identifierName);
                if (symbol) return symbol;
            }
            current = current->parent;
        }

        // If not found in any parent scope, try the root scope
        return rootScope.lookupName(identifierName);
    }

    const Symbol* findSymbolFromSyntax(const SyntaxNode* syntax) {
        if (!syntax) return nullptr;

        // Use the compilation to find symbols that were created from this syntax
        // This leverages slang's internal mapping from syntax to symbols

        // For module declarations
        if (syntax->kind == slang::syntax::SyntaxKind::ModuleDeclaration) {
            auto& moduleDecl = syntax->as<slang::syntax::ModuleDeclarationSyntax>();
            return rootScope.lookupName(moduleDecl.header->name.valueText());
        }

        // For interface declarations
        if (syntax->kind == slang::syntax::SyntaxKind::InterfaceDeclaration) {
            auto& interfaceDecl = syntax->as<slang::syntax::ModuleDeclarationSyntax>();
            return rootScope.lookupName(interfaceDecl.header->name.valueText());
        }

        // For package declarations
        if (syntax->kind == slang::syntax::SyntaxKind::PackageDeclaration) {
            auto& packageDecl = syntax->as<slang::syntax::ModuleDeclarationSyntax>();
            return rootScope.lookupName(packageDecl.header->name.valueText());
        }

        // For variable declarations
        if (syntax->kind == slang::syntax::SyntaxKind::DataDeclaration) {
            // This is more complex - we need to find the parent scope and look for the variable
            auto containingScope = findContainingScope(syntax);
            if (containingScope) {
                // Look through the scope's members to find one with matching syntax
                for (auto& member : containingScope->members()) {
                    if (member.getSyntax() == syntax) {
                        return &member;
                    }
                }
            }
        }

        // For function and task declarations
        if (syntax->kind == slang::syntax::SyntaxKind::FunctionDeclaration ||
            syntax->kind == slang::syntax::SyntaxKind::TaskDeclaration) {
            auto containingScope = findContainingScope(syntax);
            if (containingScope) {
                for (auto& member : containingScope->members()) {
                    if (member.getSyntax() == syntax) {
                        return &member;
                    }
                }
            }
        }

        // For parameter declarations
        if (syntax->kind == slang::syntax::SyntaxKind::ParameterDeclarationStatement) {
            auto containingScope = findContainingScope(syntax);
            if (containingScope) {
                for (auto& member : containingScope->members()) {
                    if (member.getSyntax() == syntax) {
                        return &member;
                    }
                }
            }
        }

        // For port declarations
        if (syntax->kind == slang::syntax::SyntaxKind::PortDeclaration) {
            auto containingScope = findContainingScope(syntax);
            if (containingScope) {
                for (auto& member : containingScope->members()) {
                    if (member.getSyntax() == syntax) {
                        return &member;
                    }
                }
            }
        }

        // For typedef declarations
        if (syntax->kind == slang::syntax::SyntaxKind::TypedefDeclaration) {
            auto containingScope = findContainingScope(syntax);
            if (containingScope) {
                for (auto& member : containingScope->members()) {
                    if (member.getSyntax() == syntax) {
                        return &member;
                    }
                }
            }
        }

        // For genvar declarations
        if (syntax->kind == slang::syntax::SyntaxKind::GenvarDeclaration) {
            auto containingScope = findContainingScope(syntax);
            if (containingScope) {
                for (auto& member : containingScope->members()) {
                    if (member.getSyntax() == syntax) {
                        return &member;
                    }
                }
            }
        }

        // For net declarations
        if (syntax->kind == slang::syntax::SyntaxKind::NetDeclaration) {
            auto containingScope = findContainingScope(syntax);
            if (containingScope) {
                for (auto& member : containingScope->members()) {
                    if (member.getSyntax() == syntax) {
                        return &member;
                    }
                }
            }
        }

        // For class declarations
        if (syntax->kind == slang::syntax::SyntaxKind::ClassDeclaration) {
            auto containingScope = findContainingScope(syntax);
            if (containingScope) {
                for (auto& member : containingScope->members()) {
                    if (member.getSyntax() == syntax) {
                        return &member;
                    }
                }
            }
        }

        // For declarator syntax nodes (used in many declarations)
        if (syntax->kind == slang::syntax::SyntaxKind::Declarator) {
            auto& declarator = syntax->as<slang::syntax::DeclaratorSyntax>();
            if (!declarator.name.isMissing()) {
                auto containingScope = findContainingScope(syntax);
                if (containingScope) {
                    // Look for a symbol with the same name
                    auto symbol = containingScope->lookupName(declarator.name.valueText());
                    if (symbol) {
                        return symbol;
                    }
                }
            }
        }

        // For identifier name syntax nodes
        if (syntax->kind == slang::syntax::SyntaxKind::IdentifierName) {
            auto& identifier = syntax->as<slang::syntax::IdentifierNameSyntax>();
            if (!identifier.identifier.isMissing()) {
                auto containingScope = findContainingScope(syntax);
                if (containingScope) {
                    auto symbol = containingScope->lookupName(identifier.identifier.valueText());
                    if (symbol) {
                        return symbol;
                    }
                }
            }
        }

        // Generic approach: try to find any symbol that was created from this syntax
        // by traversing all scopes and checking syntax associations
        return findSymbolBySyntaxTraversal(syntax);
    }

    const Symbol* findSymbolBySyntaxTraversal(const SyntaxNode* syntax) {
        if (!syntax) return nullptr;

        // Recursively traverse all scopes starting from root to find a symbol
        // that was created from the given syntax node
        return traverseScopeForSyntax(rootScope, syntax);
    }

    const Symbol* traverseScopeForSyntax(const Scope& scope, const SyntaxNode* syntax) {
        // Check all members in this scope
        for (auto& member : scope.members()) {
            // Check if this member was created from the target syntax
            if (member.getSyntax() == syntax) {
                return &member;
            }

            // If this member is also a scope, recursively search it
            if (member.isScope()) {
                auto result = traverseScopeForSyntax(member.as<Scope>(), syntax);
                if (result) return result;
            }
        }

        return nullptr;
    }

    const Scope* getScopeForSyntax(const SyntaxNode* syntax) {
        if (!syntax) return nullptr;

        // Check if this syntax node represents a scope-creating construct
        switch (syntax->kind) {
            case slang::syntax::SyntaxKind::ModuleDeclaration: {
                auto& moduleDecl = syntax->as<slang::syntax::ModuleDeclarationSyntax>();
                auto symbol = rootScope.lookupName(moduleDecl.header->name.valueText());
                if (symbol && symbol->isScope()) {
                    return &symbol->as<Scope>();
                }
                break;
            }
            case slang::syntax::SyntaxKind::InterfaceDeclaration: {
                auto& interfaceDecl = syntax->as<slang::syntax::ModuleDeclarationSyntax>();
                auto symbol = rootScope.lookupName(interfaceDecl.header->name.valueText());
                if (symbol && symbol->isScope()) {
                    return &symbol->as<Scope>();
                }
                break;
            }
            case slang::syntax::SyntaxKind::ProgramDeclaration: {
                auto& programDecl = syntax->as<slang::syntax::ModuleDeclarationSyntax>();
                auto symbol = rootScope.lookupName(programDecl.header->name.valueText());
                if (symbol && symbol->isScope()) {
                    return &symbol->as<Scope>();
                }
                break;
            }
            case slang::syntax::SyntaxKind::PackageDeclaration: {
                auto& packageDecl = syntax->as<slang::syntax::ModuleDeclarationSyntax>();
                auto symbol = rootScope.lookupName(packageDecl.header->name.valueText());
                if (symbol && symbol->isScope()) {
                    return &symbol->as<Scope>();
                }
                break;
            }
            case slang::syntax::SyntaxKind::FunctionDeclaration:
            case slang::syntax::SyntaxKind::TaskDeclaration: {
                // Functions and tasks create their own scopes
                auto containingScope = findContainingScope(syntax->parent);
                if (containingScope) {
                    for (auto& member : containingScope->members()) {
                        if (member.getSyntax() == syntax && member.isScope()) {
                            return &member.as<Scope>();
                        }
                    }
                }
                break;
            }
            case slang::syntax::SyntaxKind::ClassDeclaration: {
                // Classes create scopes
                auto containingScope = findContainingScope(syntax->parent);
                if (containingScope) {
                    for (auto& member : containingScope->members()) {
                        if (member.getSyntax() == syntax && member.isScope()) {
                            return &member.as<Scope>();
                        }
                    }
                }
                break;
            }
            default:
                break;
        }

        return nullptr;
    }

    const Scope* findContainingScope(const SyntaxNode* syntax) {
        if (!syntax) return nullptr;

        const SyntaxNode* current = syntax->parent;
        while (current) {
            auto scope = getScopeForSyntax(current);
            if (scope) return scope;
            current = current->parent;
        }

        return &rootScope;
    }

    bool isSymbolDeclaration(const SyntaxNode* syntax) {
        if (!syntax) return false;

        switch (syntax->kind) {
            // Top-level declarations
            case slang::syntax::SyntaxKind::ModuleDeclaration:
            case slang::syntax::SyntaxKind::InterfaceDeclaration:
            case slang::syntax::SyntaxKind::ProgramDeclaration:
            case slang::syntax::SyntaxKind::PackageDeclaration:
            case slang::syntax::SyntaxKind::ConfigDeclaration:

            // Variable and net declarations
            case slang::syntax::SyntaxKind::DataDeclaration:
            case slang::syntax::SyntaxKind::NetDeclaration:
            case slang::syntax::SyntaxKind::ForVariableDeclaration:

            // Parameter declarations
            case slang::syntax::SyntaxKind::ParameterDeclaration:
            case slang::syntax::SyntaxKind::SpecparamDeclaration:

            // Subroutine declarations
            case slang::syntax::SyntaxKind::FunctionDeclaration:
            case slang::syntax::SyntaxKind::TaskDeclaration:

            // Type declarations
            case slang::syntax::SyntaxKind::TypedefDeclaration:
            case slang::syntax::SyntaxKind::ClassDeclaration:

            // Port declarations
            case slang::syntax::SyntaxKind::PortDeclaration:

            // Generate declarations
            case slang::syntax::SyntaxKind::GenerateBlock:
            case slang::syntax::SyntaxKind::LoopGenerate:
            case slang::syntax::SyntaxKind::IfGenerate:
            case slang::syntax::SyntaxKind::CaseGenerate:

            // Instance declarations
            case slang::syntax::SyntaxKind::HierarchyInstantiation:
            case slang::syntax::SyntaxKind::CheckerInstantiation:

            // Covergroup declarations
            case slang::syntax::SyntaxKind::CovergroupDeclaration:
            case slang::syntax::SyntaxKind::SequenceDeclaration:
            case slang::syntax::SyntaxKind::PropertyDeclaration:
            case slang::syntax::SyntaxKind::CheckerDeclaration:
                return true;
            default:
                return false;
        }
    }

    bool isIdentifierReference(const SyntaxNode* syntax) {
        if (!syntax) return false;

        switch (syntax->kind) {
            // Basic identifier references
            case slang::syntax::SyntaxKind::IdentifierName:
            case slang::syntax::SyntaxKind::IdentifierSelectName:
            case slang::syntax::SyntaxKind::ScopedName:

            // Member access expressions
            case slang::syntax::SyntaxKind::MemberAccessExpression:
            case slang::syntax::SyntaxKind::ElementSelectExpression:

            // Hierarchical references
            case slang::syntax::SyntaxKind::HierarchicalInstance:

            // System identifier references
            case slang::syntax::SyntaxKind::SystemName:
                return true;
            default:
                return false;
        }
    }

    std::string getIdentifierName(const SyntaxNode* syntax) {
        if (!syntax) return "";

        switch (syntax->kind) {
            case slang::syntax::SyntaxKind::IdentifierName: {
                auto& identName = syntax->as<slang::syntax::IdentifierNameSyntax>();
                return std::string(identName.identifier.valueText());
            }
            case slang::syntax::SyntaxKind::IdentifierSelectName: {
                auto& selectName = syntax->as<slang::syntax::IdentifierSelectNameSyntax>();
                return std::string(selectName.identifier.valueText());
            }
            case slang::syntax::SyntaxKind::ScopedName: {
                auto& scopedName = syntax->as<slang::syntax::ScopedNameSyntax>();
                // For scoped names, return the final identifier
                if (scopedName.right) {
                    return getIdentifierName(scopedName.right);
                }
                return "";
            }
            case slang::syntax::SyntaxKind::MemberAccessExpression: {
                auto& memberAccess = syntax->as<slang::syntax::MemberAccessExpressionSyntax>();
                return std::string(memberAccess.name.valueText());
            }
            case slang::syntax::SyntaxKind::SystemName: {
                auto& systemName = syntax->as<slang::syntax::SystemNameSyntax>();
                return std::string(systemName.systemIdentifier.valueText());
            }
            default:
                return "";
        }
    }

    std::string getQualifiedName(const SyntaxNode* syntax) {
        if (!syntax) return "";

        std::vector<std::string> nameParts;
        const SyntaxNode* current = syntax;

        while (current) {
            switch (current->kind) {
                case slang::syntax::SyntaxKind::ModuleDeclaration: {
                    auto& moduleDecl = current->as<slang::syntax::ModuleDeclarationSyntax>();
                    nameParts.push_back(std::string(moduleDecl.header->name.valueText()));
                    break;
                }
                case slang::syntax::SyntaxKind::InterfaceDeclaration: {
                    auto& interfaceDecl = current->as<slang::syntax::ModuleDeclarationSyntax>();
                    nameParts.push_back(std::string(interfaceDecl.header->name.valueText()));
                    break;
                }
                case slang::syntax::SyntaxKind::PackageDeclaration: {
                    auto& packageDecl = current->as<slang::syntax::ModuleDeclarationSyntax>();
                    nameParts.push_back(std::string(packageDecl.header->name.valueText()));
                    break;
                }
                case slang::syntax::SyntaxKind::ClassDeclaration: {
                    auto& classDecl = current->as<slang::syntax::ClassDeclarationSyntax>();
                    nameParts.push_back(std::string(classDecl.name.valueText()));
                    break;
                }
                case slang::syntax::SyntaxKind::FunctionDeclaration: {
                    auto& funcDecl = current->as<slang::syntax::FunctionDeclarationSyntax>();
                    if (funcDecl.prototype && funcDecl.prototype->name) {
                        // Function name handling - simplified for now
                        nameParts.push_back("function");
                    }
                    break;
                }
                case slang::syntax::SyntaxKind::TaskDeclaration: {
                    // Task declarations use the same syntax as functions in slang
                    auto& taskDecl = current->as<slang::syntax::FunctionDeclarationSyntax>();
                    if (taskDecl.prototype && taskDecl.prototype->name) {
                        nameParts.push_back("task");
                    }
                    break;
                }
                case slang::syntax::SyntaxKind::GenerateBlock: {
                    auto& genBlock = current->as<slang::syntax::GenerateBlockSyntax>();
                    if (genBlock.label) {
                        nameParts.push_back(std::string(genBlock.label->name.valueText()));
                    }
                    break;
                }
                default:
                    break;
            }
            current = current->parent;
        }

        std::string result;
        for (auto it = nameParts.rbegin(); it != nameParts.rend(); ++it) {
            if (!result.empty()) result += ".";
            result += *it;
        }

        return result;
    }

private:
    Compilation& compilation;
    const Scope& rootScope;
};

// Implementation of public interface
SymbolResolver::SymbolResolver(Compilation& compilation)
    : pImpl(std::make_unique<Impl>(compilation)) {
}

SymbolResolver::~SymbolResolver() = default;

const Symbol* SymbolResolver::resolveSymbol(const SyntaxNode* syntax) {
    return pImpl->resolveSymbol(syntax);
}

const Symbol* SymbolResolver::resolveIdentifier(const SyntaxNode* identifierNode) {
    return pImpl->resolveIdentifier(identifierNode);
}

const Symbol* SymbolResolver::findSymbolFromSyntax(const SyntaxNode* syntax) {
    return pImpl->findSymbolFromSyntax(syntax);
}

const Scope* SymbolResolver::getScopeForSyntax(const SyntaxNode* syntax) {
    return pImpl->getScopeForSyntax(syntax);
}

const Scope* SymbolResolver::findContainingScope(const SyntaxNode* syntax) {
    return pImpl->findContainingScope(syntax);
}

bool SymbolResolver::isSymbolDeclaration(const SyntaxNode* syntax) {
    return pImpl->isSymbolDeclaration(syntax);
}

bool SymbolResolver::isIdentifierReference(const SyntaxNode* syntax) {
    return pImpl->isIdentifierReference(syntax);
}

std::string SymbolResolver::getIdentifierName(const SyntaxNode* syntax) {
    return pImpl->getIdentifierName(syntax);
}

std::string SymbolResolver::getQualifiedName(const SyntaxNode* syntax) {
    return pImpl->getQualifiedName(syntax);
}

} // namespace syntax_symbol_mapper
