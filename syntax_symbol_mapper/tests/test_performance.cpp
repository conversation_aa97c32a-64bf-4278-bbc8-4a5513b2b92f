#include <iostream>
#include <string>
#include <chrono>
#include <sstream>
#include <cassert>
#include <iomanip>

#include "SyntaxToSymbolMapper.h"
#include "slang/syntax/SyntaxTree.h"
#include "slang/ast/Compilation.h"

using namespace syntax_symbol_mapper;
using namespace slang;
using namespace std::chrono;

std::string generateLargeSystemVerilogCode(int numModules, int numVarsPerModule) {
    std::ostringstream oss;

    // Generate a package with common types
    oss << "package common_pkg;\n";
    oss << "    typedef struct {\n";
    oss << "        int x, y, z;\n";
    oss << "    } point3d_t;\n";
    oss << "    \n";
    oss << "    typedef enum {\n";
    oss << "        STATE_IDLE, STATE_ACTIVE, STATE_DONE\n";
    oss << "    } state_t;\n";
    oss << "    \n";
    oss << "    parameter int COMMON_WIDTH = 32;\n";
    oss << "endpackage\n\n";

    // Generate multiple modules
    for (int i = 0; i < numModules; i++) {
        oss << "module test_module_" << i << " #(\n";
        oss << "    parameter int WIDTH = 8,\n";
        oss << "    parameter int DEPTH = 16\n";
        oss << ") (\n";
        oss << "    input logic clk,\n";
        oss << "    input logic reset,\n";
        oss << "    input logic [WIDTH-1:0] data_in,\n";
        oss << "    output logic [WIDTH-1:0] data_out\n";
        oss << ");\n";
        oss << "    import common_pkg::*;\n\n";

        // Generate variables
        for (int j = 0; j < numVarsPerModule; j++) {
            oss << "    logic [WIDTH-1:0] var_" << j << ";\n";
            oss << "    int counter_" << j << ";\n";
        }

        oss << "    point3d_t my_point;\n";
        oss << "    state_t current_state;\n\n";

        // Generate functions
        oss << "    function automatic int calc_sum_" << i << "(int a, int b);\n";
        oss << "        return a + b + " << i << ";\n";
        oss << "    endfunction\n\n";

        oss << "    function automatic logic [WIDTH-1:0] process_data_" << i << "(logic [WIDTH-1:0] input_data);\n";
        oss << "        return input_data ^ " << i << ";\n";
        oss << "    endfunction\n\n";

        // Generate always blocks
        oss << "    always_ff @(posedge clk or posedge reset) begin\n";
        oss << "        if (reset) begin\n";
        for (int j = 0; j < numVarsPerModule; j++) {
            oss << "            var_" << j << " <= 0;\n";
            oss << "            counter_" << j << " <= 0;\n";
        }
        oss << "            current_state <= STATE_IDLE;\n";
        oss << "        end else begin\n";
        oss << "            case (current_state)\n";
        oss << "                STATE_IDLE: begin\n";
        oss << "                    current_state <= STATE_ACTIVE;\n";
        for (int j = 0; j < std::min(3, numVarsPerModule); j++) {
            oss << "                    var_" << j << " <= data_in;\n";
        }
        oss << "                end\n";
        oss << "                STATE_ACTIVE: begin\n";
        for (int j = 0; j < std::min(3, numVarsPerModule); j++) {
            oss << "                    var_" << j << " <= process_data_" << i << "(var_" << j << ");\n";
            oss << "                    counter_" << j << " <= counter_" << j << " + 1;\n";
        }
        oss << "                    if (counter_0 > 100) current_state <= STATE_DONE;\n";
        oss << "                end\n";
        oss << "                STATE_DONE: begin\n";
        oss << "                    current_state <= STATE_IDLE;\n";
        oss << "                end\n";
        oss << "            endcase\n";
        oss << "        end\n";
        oss << "    end\n\n";

        // Generate combinational logic
        oss << "    always_comb begin\n";
        oss << "        data_out = var_0";
        for (int j = 1; j < std::min(4, numVarsPerModule); j++) {
            oss << " ^ var_" << j;
        }
        oss << ";\n";
        oss << "    end\n\n";

        oss << "endmodule\n\n";
    }

    // Generate a top-level module that instantiates others
    oss << "module top_level;\n";
    oss << "    logic clk, reset;\n";
    oss << "    logic [7:0] data_bus;\n\n";

    for (int i = 0; i < std::min(5, numModules); i++) {
        oss << "    logic [7:0] data_" << i << "_in, data_" << i << "_out;\n";
        oss << "    test_module_" << i << " #(.WIDTH(8), .DEPTH(32)) inst_" << i << " (\n";
        oss << "        .clk(clk),\n";
        oss << "        .reset(reset),\n";
        oss << "        .data_in(data_" << i << "_in),\n";
        oss << "        .data_out(data_" << i << "_out)\n";
        oss << "    );\n\n";
    }

    oss << "    // Clock generation\n";
    oss << "    initial begin\n";
    oss << "        clk = 0;\n";
    oss << "        forever #5 clk = ~clk;\n";
    oss << "    end\n\n";

    oss << "    // Reset generation\n";
    oss << "    initial begin\n";
    oss << "        reset = 1;\n";
    oss << "        #100 reset = 0;\n";
    oss << "    end\n\n";

    oss << "endmodule\n";

    return oss.str();
}

void test_performance_small() {
    std::cout << "=== Small Scale Performance Test ===" << std::endl;

    auto source = generateLargeSystemVerilogCode(3, 5);
    std::cout << "  Generated code size: " << source.length() << " characters" << std::endl;

    auto start = high_resolution_clock::now();

    auto tree = syntax::SyntaxTree::fromText(source);
    assert(tree != nullptr);

    ast::Compilation compilation;
    compilation.addSyntaxTree(tree);
    auto diagnostics = compilation.getAllDiagnostics();

    auto parse_end = high_resolution_clock::now();

    MapperConfig config;
    config.enable_caching = true;
    config.deep_traversal = true;

    SyntaxToSymbolMapper mapper(compilation, config);
    auto result = mapper.buildMapping(*tree);

    auto mapping_end = high_resolution_clock::now();

    assert(result.success);

    auto parse_time = duration_cast<milliseconds>(parse_end - start);
    auto mapping_time = duration_cast<milliseconds>(mapping_end - parse_end);
    auto total_time = duration_cast<milliseconds>(mapping_end - start);

    std::cout << "  ✓ Parse time: " << parse_time.count() << "ms" << std::endl;
    std::cout << "  ✓ Mapping time: " << mapping_time.count() << "ms" << std::endl;
    std::cout << "  ✓ Total time: " << total_time.count() << "ms" << std::endl;
    std::cout << "  ✓ Mappings created: " << mapper.getMappingCount() << std::endl;
    std::cout << "  ✓ Compilation issues: " << diagnostics.size() << std::endl;
}

void test_performance_medium() {
    std::cout << "\n=== Medium Scale Performance Test ===" << std::endl;

    auto source = generateLargeSystemVerilogCode(10, 10);
    std::cout << "  Generated code size: " << source.length() << " characters" << std::endl;

    auto start = high_resolution_clock::now();

    auto tree = syntax::SyntaxTree::fromText(source);
    assert(tree != nullptr);

    ast::Compilation compilation;
    compilation.addSyntaxTree(tree);
    auto diagnostics = compilation.getAllDiagnostics();

    auto parse_end = high_resolution_clock::now();

    MapperConfig config;
    config.enable_caching = true;
    config.deep_traversal = true;
    config.max_cache_size = 5000;

    SyntaxToSymbolMapper mapper(compilation, config);
    auto result = mapper.buildMapping(*tree);

    auto mapping_end = high_resolution_clock::now();

    assert(result.success);

    auto parse_time = duration_cast<milliseconds>(parse_end - start);
    auto mapping_time = duration_cast<milliseconds>(mapping_end - parse_end);
    auto total_time = duration_cast<milliseconds>(mapping_end - start);

    std::cout << "  ✓ Parse time: " << parse_time.count() << "ms" << std::endl;
    std::cout << "  ✓ Mapping time: " << mapping_time.count() << "ms" << std::endl;
    std::cout << "  ✓ Total time: " << total_time.count() << "ms" << std::endl;
    std::cout << "  ✓ Mappings created: " << mapper.getMappingCount() << std::endl;
    std::cout << "  ✓ Compilation issues: " << diagnostics.size() << std::endl;
}

void test_cache_performance() {
    std::cout << "\n=== Cache Performance Test ===" << std::endl;

    auto source = generateLargeSystemVerilogCode(5, 8);
    auto tree = syntax::SyntaxTree::fromText(source);
    ast::Compilation compilation;
    compilation.addSyntaxTree(tree);

    // Test with cache enabled
    auto start_cached = high_resolution_clock::now();

    MapperConfig config_cached;
    config_cached.enable_caching = true;
    config_cached.deep_traversal = true;

    SyntaxToSymbolMapper mapper_cached(compilation, config_cached);
    auto result_cached = mapper_cached.buildMapping(*tree);

    auto end_cached = high_resolution_clock::now();

    // Test with cache disabled
    auto start_uncached = high_resolution_clock::now();

    MapperConfig config_uncached;
    config_uncached.enable_caching = false;
    config_uncached.deep_traversal = true;

    SyntaxToSymbolMapper mapper_uncached(compilation, config_uncached);
    auto result_uncached = mapper_uncached.buildMapping(*tree);

    auto end_uncached = high_resolution_clock::now();

    assert(result_cached.success);
    assert(result_uncached.success);

    auto time_cached = duration_cast<microseconds>(end_cached - start_cached);
    auto time_uncached = duration_cast<microseconds>(end_uncached - start_uncached);

    std::cout << "  ✓ With cache: " << time_cached.count() << "μs" << std::endl;
    std::cout << "  ✓ Without cache: " << time_uncached.count() << "μs" << std::endl;
    std::cout << "  ✓ Mappings (cached): " << mapper_cached.getMappingCount() << std::endl;
    std::cout << "  ✓ Mappings (uncached): " << mapper_uncached.getMappingCount() << std::endl;

    if (time_cached.count() > 0 && time_uncached.count() > 0) {
        double speedup = static_cast<double>(time_uncached.count()) / time_cached.count();
        std::cout << "  ✓ Cache speedup: " << std::fixed << std::setprecision(2) << speedup << "x" << std::endl;
    }
}

int main() {
    std::cout << "SyntaxToSymbolMapper Performance Tests" << std::endl;
    std::cout << "=====================================" << std::endl;

    try {
        test_performance_small();
        test_performance_medium();
        test_cache_performance();

        std::cout << "\n✓ All performance tests completed!" << std::endl;
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "\n✗ Performance test failed: " << e.what() << std::endl;
        return 1;
    }
}
